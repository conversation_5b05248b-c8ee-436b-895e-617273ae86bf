import { User } from "../../typings/globals";
import { selectUser } from "./getArrayElement";

export const getCleanArray = (
  baseArray: User[],
  excludedArray: User[],
): User[] => {
  return baseArray.filter((item) => {
    return !excludedArray.some((excludedItem) => {
      return item.username == excludedItem.username;
    });
  });
};

export const getCleanUser = (
  baseArray: User[],
  excludedArray: User[],
  vuId: number,
): User => {
  const cleanArray = getCleanArray(baseArray, excludedArray);
  return selectUser(vuId, cleanArray);
};

export const getNextUser = (vuId: number, baseArray: User[]): User => {
  try {
    const excludedArray: User[] = globalThis.excludedUsers;
    return getCleanUser(baseArray, excludedArray, vuId);
  } catch (error: any) {
    console.error(`Error getting next user: ${error.message}`);
    return selectUser(vuId, baseArray);
  }
};

export const addUserToExcluded = (user: User): void => {
  try {
    globalThis.excludedUsers.push(user);
  } catch (error: any) {
    console.error(`Error adding user to excluded array: ${error.message}`);
  }
};
