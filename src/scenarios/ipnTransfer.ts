import { fail, sleep } from "k6";
import { login, logout } from "../actions/login";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import { getNextUser } from "../core/helpers/getExcludedArray";
import {
  performIpnTransfer,
  getIpnTransfersList,
  getBanksList,
  getTransferReasons,
  simulateIpnTransferFees,
  getIpnBeneficiaries,
  getIpnCategories,
  getIpnTransferById, createIpnBeneficiary, deleteIpnBeneficiary, callWebHook
} from "../actions/ipn";
import exec from "k6/execution";
import {listAccountsForLocalTransfer} from "../actions/accounts";
import {getEligibleLocalTransferAccount} from "../actions/accounts/accounts-utils";


const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 200;


/**
 * Main execution function for the IPN transfer scenario
 */
export function ipnTransferExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.ipnTransferSubUsers,
  );

  try {
    if (!user.username || !user.password) {
      console.log(`Warning: User with id ${exec.vu.idInTest} has invalid username or password. Data file: ipnTransferSubUsers`);
      // Skip this iteration but don't fail the test
      return;
    }

    const deviceId = `${user.username}_deviceId5`;
    const token = login(user.username, user.password, deviceId);

    sleep(waitTimeAfterLogin);

    if (token) {
      const customHeaders = { token, deviceId };

      // Call homepage to simulate user navigation
      callHomepage(customHeaders);

      // Get banks list and transfer reasons
      getBanksList(customHeaders);
      getTransferReasons(customHeaders);
      const listOfTransferAccounts: any = listAccountsForLocalTransfer(customHeaders);
      const eligibleLocalTransferAccount = getEligibleLocalTransferAccount(
          listOfTransferAccounts,
      );
      // // Get IPN categories
      getIpnCategories(customHeaders);

      // // Get existing transfers list and beneficiaries
      getIpnTransfersList(customHeaders);

      const transferBody = {
        recipient: {
          type: "BANK_ACCOUNT",
          bankAccountRecipient: {
            beneficiaryName: "EBC",
            bankId: "31",
            accountNumber: "****************"
          }
        },
        nickname: "Test Recipient",
        amount: {
          amount: 10,
          currency: "EGP"
        },
        sender: {
              "accountId": eligibleLocalTransferAccount.id,
              "accountNumber": eligibleLocalTransferAccount.accountNumber
        },
        feesChargesOn: "SENDER",
        reasonForTransfer: "Family Support",
        category: "other",
        description: null,
        isFavorite: false,
        note: "k6 load test",
        otp: "111221"
      };


      //Create IpnBeneficiary

      createIpnBeneficiary(transferBody , customHeaders)
      // //  Simulate fees before performing the transfer
      simulateIpnTransferFees(transferBody, customHeaders);
      const allBeneficiaries : any =  getIpnBeneficiaries(customHeaders);



      // Perform the actual transfer
      const transferResponse = performIpnTransfer(transferBody, customHeaders);

      // If transfer is successful, get the transfer details
      if (transferResponse.status === 201) {

        try {
          const transferData = JSON.parse(transferResponse.body as string);
          if (transferData && transferData.transferGlobalId) {
            getIpnTransferById(transferData.transferGlobalId, customHeaders);
            sleep(2)
            const webHookBody = {
              "status":"SUCCESSFUL",
              "referenceNumber":`${transferData.transferGlobalId}-test`
            }
            callWebHook(transferData.transferGlobalId, webHookBody, customHeaders)
          }
        } catch (e) {
          console.log("Failed to parse transfer response JSON");
        }
      }
      if (allBeneficiaries != null) {
        if (allBeneficiaries?.length > 0) {
          const beneficiaryID = allBeneficiaries[allBeneficiaries.length - 1].beneficiaryGlobalId
          deleteIpnBeneficiary(beneficiaryID , customHeaders)
        }
      }

      // Logout to clean up the session
      logout(customHeaders);
    } else {
      console.log("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    sleep(waitTimeAfterVu);
  }
}

/**
 * Configures the load test scenario based on the specified type
 * @param type The type of load test to configure
 * @returns The load test configuration
 */
export function ipnTransfer(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "ipnTransfer");

  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 1,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: ipnTransferExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 1,
        stages: [
          { duration: "1h", target: targetVUs > 0 ? targetVUs : 5 },
          { duration: "12h", target: targetVUs > 0 ? targetVUs : 5 },
          { duration: "1h", target: 0 },
        ],
        exec: ipnTransferExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 2,
        iterations: 1,
        exec: ipnTransferExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 1,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
          { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
        ],
        exec: ipnTransferExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}