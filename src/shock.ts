import { setupVu } from "./core/setup";
import {
  createTransferInsideCIB,
  createTransferInsideCIBExec,
  accessHomePageExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  creditCardTransaction,
  creditCardTransactionExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  accountTransaction,
  accountTransactionExec,
  bookCertificateDepositExec,
  createOutSideCibTransfer,
  createTransferOutsideCIBExec,
  creditCardTransfer,
  creditCardTransferExec,
  billPaymentExec,
  depositDetailsExec,
  creditCardInstallementExec,
  creditCardInstallment,
  accountTransactionDetails,
  accountTransactionDetailsExec,
  debitCardListExec,
  debitCardList, ipnTransfer,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userLoggingIn: accessHomePageMs("shock"),
    creditCardTransaction: creditCardTransfer("shock"),
    userAccountMovement: accountTransaction("shock"),
    userAccountMovementDetails: accountTransactionDetails("shock"),
    userCreditCardInstallment: creditCardInstallment("shock"),
    userCreditCardMovement: creditCardTransaction("shock"),
    userSubscribingToEstatement: estatementEnrollment("shock"),
    userTransferInsideCib: createTransferInsideCIB("shock"),
    userTransferOutsideCib: createOutSideCibTransfer("shock"),
    debitCardList: debitCardList("shock"),
    userIpnTransfer: ipnTransfer("shock"),

  },
  thresholds: {
    //failure rate should be less than 10%
    http_req_failed: ["rate<0.1"],
  },
};

export {
  creditCardTransactionExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageExec,
  accessHomePageCr2Exec,
  estatementEnrollmentExec,
  accountTransactionExec,
  bookCertificateDepositExec,
  creditCardTransferExec,
  billPaymentExec,
  depositDetailsExec,
  creditCardInstallementExec,
  accountTransactionDetailsExec,
  debitCardListExec,
};
